import { dexieDatabase } from '../database/dexie'
import { ChatPrompt } from '@/common/types/database_entity'

/**
 * 聊天提示词数据访问对象
 * 只负责数据库CRUD操作，不包含业务逻辑
 */
export class ChatPromptDao {
  private static instance: ChatPromptDao

  public static getInstance(): ChatPromptDao {
    if (!ChatPromptDao.instance) {
      ChatPromptDao.instance = new ChatPromptDao()
    }
    return ChatPromptDao.instance
  }

  /**
   * 创建聊天提示词记录
   */
  async create(chatPrompt: Omit<ChatPrompt, 'id'>): Promise<ChatPrompt> {
    await dexieDatabase.initialize()
    
    const id = await dexieDatabase.chatPrompt.add({
      ...chatPrompt,
      create_time: chatPrompt.create_time || Date.now(),
      is_synced: chatPrompt.is_synced || 0,
      is_delete: 0
    })
    
    const created = await dexieDatabase.chatPrompt.get(id)
    if (!created) {
      throw new Error('Failed to create chat prompt')
    }
    
    return created
  }

  /**
   * 根据ID查找聊天提示词记录
   */
  async findById(id: number): Promise<ChatPrompt | null> {
    await dexieDatabase.initialize()
    
    const chatPrompt = await dexieDatabase.chatPrompt.get(id)
    return chatPrompt || null
  }

  /**
   * 根据chat_uid查找聊天提示词记录
   */
  async findByChatUid(chatUid: string): Promise<ChatPrompt | null> {
    await dexieDatabase.initialize()
    
    const chatPrompt = await dexieDatabase.chatPrompt
      .where('chat_uid')
      .equals(chatUid)
      .and(item => item.is_delete === 0)
      .first()
    
    return chatPrompt || null
  }

  /**
   * 根据提示词内容查找记录
   */
  async findByPrompt(prompt: string): Promise<ChatPrompt | null> {
    await dexieDatabase.initialize()
    
    const chatPrompt = await dexieDatabase.chatPrompt
      .where('chat_prompt')
      .equals(prompt)
      .and(item => item.is_delete === 0)
      .first()
    
    return chatPrompt || null
  }

  /**
   * 查找所有聊天提示词记录
   */
  async findAll(options: {
    limit?: number
    offset?: number
    orderBy?: 'create_time' | 'id'
    orderDirection?: 'ASC' | 'DESC'
  } = {}): Promise<ChatPrompt[]> {
    await dexieDatabase.initialize()
    
    const { limit = 50, offset = 0 } = options
    
    return await dexieDatabase.chatPrompt
      .where('is_delete')
      .equals(0)
      .offset(offset)
      .limit(limit)
      .reverse() // 默认按时间倒序
      .toArray()
  }

  /**
   * 更新聊天提示词记录
   */
  async update(id: number, updates: Partial<ChatPrompt>): Promise<ChatPrompt> {
    await dexieDatabase.initialize()
    
    const existing = await dexieDatabase.chatPrompt.get(id)
    if (!existing) {
      throw new Error(`Chat prompt with id ${id} not found`)
    }
    
    await dexieDatabase.chatPrompt.update(id, updates)
    
    const updated = await dexieDatabase.chatPrompt.get(id)
    if (!updated) {
      throw new Error('Failed to update chat prompt')
    }
    
    return updated
  }

  /**
   * 软删除聊天提示词记录
   */
  async softDelete(id: number): Promise<boolean> {
    await dexieDatabase.initialize()
    
    const existing = await dexieDatabase.chatPrompt.get(id)
    if (!existing) {
      return false
    }
    
    await dexieDatabase.chatPrompt.update(id, { is_delete: 1 })
    return true
  }

  /**
   * 硬删除聊天提示词记录
   */
  async delete(id: number): Promise<boolean> {
    await dexieDatabase.initialize()
    
    await dexieDatabase.chatPrompt.delete(id)
    return true
  }

  /**
   * 根据chat_uid软删除记录
   */
  async softDeleteByChatUid(chatUid: string): Promise<boolean> {
    await dexieDatabase.initialize()
    
    const record = await this.findByChatUid(chatUid)
    if (!record) {
      return false
    }
    
    await dexieDatabase.chatPrompt.update(record.id!, { is_delete: 1 })
    return true
  }

  /**
   * 统计聊天提示词记录数量
   */
  async count(): Promise<number> {
    await dexieDatabase.initialize()
    
    return await dexieDatabase.chatPrompt
      .where('is_delete')
      .equals(0)
      .count()
  }

  /**
   * 批量创建聊天提示词记录
   */
  async bulkCreate(chatPrompts: Omit<ChatPrompt, 'id'>[]): Promise<ChatPrompt[]> {
    await dexieDatabase.initialize()
    
    const now = Date.now()
    const records = chatPrompts.map(prompt => ({
      ...prompt,
      create_time: prompt.create_time || now,
      is_synced: prompt.is_synced || 0,
      is_delete: 0
    }))
    
    const ids = await dexieDatabase.chatPrompt.bulkAdd(records, { allKeys: true })
    
    const created = await dexieDatabase.chatPrompt
      .where('id')
      .anyOf(ids as number[])
      .toArray()
    
    return created
  }

  /**
   * 搜索聊天提示词记录（按提示词内容）
   */
  async searchByPrompt(searchTerm: string, options: {
    limit?: number
  } = {}): Promise<ChatPrompt[]> {
    await dexieDatabase.initialize()
    
    const { limit = 50 } = options
    
    return await dexieDatabase.chatPrompt
      .where('is_delete')
      .equals(0)
      .filter(item => 
        item.chat_prompt.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .limit(limit)
      .toArray()
  }

  /**
   * 检查提示词是否存在
   */
  async exists(prompt: string): Promise<boolean> {
    await dexieDatabase.initialize()
    
    const count = await dexieDatabase.chatPrompt
      .where('chat_prompt')
      .equals(prompt)
      .and(item => item.is_delete === 0)
      .count()
    
    return count > 0
  }

  /**
   * 获取最新的提示词记录
   */
  async findLatest(limit: number = 10): Promise<ChatPrompt[]> {
    await dexieDatabase.initialize()
    
    return await dexieDatabase.chatPrompt
      .where('is_delete')
      .equals(0)
      .reverse() // 按时间倒序
      .limit(limit)
      .toArray()
  }

  /**
   * 根据时间范围查找提示词记录
   */
  async findByTimeRange(startTime: number, endTime: number): Promise<ChatPrompt[]> {
    await dexieDatabase.initialize()
    
    return await dexieDatabase.chatPrompt
      .where('create_time')
      .between(startTime, endTime)
      .and(item => item.is_delete === 0)
      .toArray()
  }

  /**
   * 获取所有唯一的chat_uid
   */
  async getAllChatUids(): Promise<string[]> {
    await dexieDatabase.initialize()
    
    const prompts = await dexieDatabase.chatPrompt
      .where('is_delete')
      .equals(0)
      .toArray()
    
    const uniqueUids = [...new Set(prompts.map(p => p.chat_uid))]
    return uniqueUids
  }
}

// 导出单例实例
export const chatPromptDao = ChatPromptDao.getInstance()
