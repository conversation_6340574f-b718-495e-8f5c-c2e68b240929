---
type: "development_rules"
description: "中介者模式下的选择器管理和组件协调规则"
---

# 中介者模式和选择器管理规则

## 核心设计原则

### BaseAIAdapter 作为唯一中介者
- 所有 Content 子模块必须通过 BaseAIAdapter 进行通信
- 禁止子模块间的直接交互
- BaseAIAdapter 负责协调所有子模块的行为

### 选择器统一管理
- 平台特定选择器由具体 Adapter 提供
- 通用选择器在 CommonSelectors 中定义
- 通过 mergeSelectors 方法统一合并
- 平台选择器优先级高于通用选择器

## BaseAIAdapter 中介者实现规则

### 基础结构要求
- 位置：extension/src/content/adapters/BaseAIAdapter.ts
- 职责：作为所有子模块的协调中心
- 子模块管理：维护 InputCapture、FloatingBubbleInject、ArchiveButtonInject 实例
- 选择器合并：实现 mergeSelectors() 方法合并平台和通用选择器
- 事件协调：通过 setupEventListeners() 统一处理子模块事件

### 抽象方法要求
- getSelectors()：子类必须实现，返回平台特定选择器
- 选择器格式：返回 PlatformConfigType 类型
- 优先级：平台选择器在前，通用选择器在后

### 子模块初始化规则
- initCapture()：初始化输入捕捉模块
- initInject()：初始化所有注入模块
- 依赖注入：将 adapter 实例传递给子模块构造函数
- 生命周期：提供 destroy() 方法清理所有子模块

### 事件监听规则
- 监听 echosync:input-changed 事件
- 监听 echosync:prompt-send 事件
- 监听 echosync:floating-bubble-clicked 事件
- 通过事件处理方法协调子模块响应

## 具体平台适配器规则

### 继承要求
- 必须继承 BaseAIAdapter
- 实现 getSelectors() 抽象方法
- 可重写事件处理方法添加平台特有逻辑

### 选择器定义规则
- 提供平台特定的 inputField 选择器
- 提供平台特定的 sendButton 选择器
- 提供平台特定的 messageContainer 选择器
- 可添加平台独有的选择器类型

### 平台特有处理
- 可重写 handlePromptSend() 添加特殊逻辑
- 可重写 handleInputChanged() 处理输入变化
- 可添加平台特有的私有方法
- 必须调用 super 方法保持基础功能

## 选择器配置管理规则

### 通用选择器定义
- 位置：extension/src/content/configs/CommonSelectors.ts
- 作用：提供跨平台通用的选择器
- 格式：导出 PlatformConfigType 类型的常量
- 内容：包含常见的输入框、按钮、容器选择器

### 选择器优先级规则
- 平台特定选择器：最高优先级，优先匹配
- 通用选择器：降级方案，平台选择器失败时使用
- 合并策略：平台选择器在前，通用选择器在后
- 匹配顺序：按数组顺序依次尝试匹配

### 选择器合并实现
- 在构造函数中调用 mergeSelectors()
- 使用扩展运算符合并数组
- 存储在 mergedSelectors 属性中
- 通过 getMergedSelectors() 方法提供访问

## 组件间通信规则

### 事件驱动通信
- 子模块 → 中介者：通过 CustomEvent 派发事件
- 中介者 → 子模块：通过方法调用或属性设置
- 事件命名：使用 echosync: 前缀
- 事件数据：通过 detail 属性传递

### 禁止的通信方式
- 子模块间直接引用
- 子模块间直接方法调用
- 绕过中介者的直接通信
- 全局变量共享状态

### 正确的通信方式
- 通过 document.dispatchEvent 派发事件
- 通过 document.addEventListener 监听事件
- 事件数据结构化传递
- 中介者统一协调响应

## 新增平台适配器规则

### 创建步骤
1. 继承 BaseAIAdapter 类
2. 实现 getSelectors() 方法
3. 提供平台特定选择器配置
4. 在 ContentScriptManager 中注册
5. 测试选择器有效性

### 文件组织
- 位置：extension/src/content/adapters/platformName.ts
- 命名：PlatformNameAdapter 类
- 导出：默认导出适配器类
- 大小：单文件不超过 300 行

### 选择器测试
- 使用 SelectorTester 工具测试
- 验证选择器在目标平台有效性
- 检查选择器稳定性
- 避免过于复杂的 CSS 选择器

## 选择器调试和优化规则

### 调试工具使用
- 使用 SelectorTester.testSelectors() 测试
- 使用 SelectorPerformance.measureFind() 监控性能
- 记录选择器查找时间和成功率
- 输出详细的调试信息

### 性能优化策略
- 优先使用高效的选择器
- 避免复杂的 CSS 选择器
- 缓存查找结果
- 监控选择器性能

### 错误处理规范
- 捕获选择器查找异常
- 记录失败的选择器
- 提供降级处理方案
- 不阻断其他功能执行

## 开发检查清单

### 新增平台适配器检查
- [ ] 继承 BaseAIAdapter
- [ ] 实现 getSelectors() 抽象方法
- [ ] 提供平台特定的选择器配置
- [ ] 在 ContentScriptManager 中注册
- [ ] 测试选择器的有效性
- [ ] 添加平台特有的处理逻辑（如需要）
- [ ] 遵循中介者模式，不直接与子模块交互
- [ ] 文件大小不超过 300 行

### 修改选择器检查
- [ ] 优先修改平台特定选择器
- [ ] 通用选择器作为降级方案
- [ ] 测试选择器在目标平台的有效性
- [ ] 考虑选择器的稳定性和兼容性
- [ ] 避免过于复杂的CSS选择器
- [ ] 记录选择器变更的原因

### 事件处理检查
- [ ] 使用 echosync: 前缀命名事件
- [ ] 通过 detail 属性传递事件数据
- [ ] 在中介者中统一处理事件
- [ ] 避免子模块间直接通信
- [ ] 添加适当的错误处理
- [ ] 及时清理事件监听器


