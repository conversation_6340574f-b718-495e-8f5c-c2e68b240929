import { Conversation } from "@/common/types";
import { Platform } from "@/common/types/database_entity";
import {
  PlatformConfig,
  SelectorMatchResult,
  RegexMatchResult,
  SelectorConfig,
} from "@/content/types/PlatformConfigType";
import { FloatingBubbleInject } from "@/content/inject/FloatingBubbleInject";
import { ArchiveButtonInject } from "@/content/inject/ArchiveButtonInject";
import { InputCapture } from "@/content/capture/InputCapture";
import { CommonSelectors } from "@/content/configs/CommonSelectors";

/**
 * 重构后的AI适配器基类
 * 作为 content 模块的核心应用上下文，统一管理组件间交互
 */
export abstract class BaseAIAdapter {
  // 平台配置
  protected config: PlatformConfig;
  protected currentPlatform: Platform = null;
  public mergedSelectors: SelectorConfig = null;

  // 捕捉页面信息模块
  protected inputCapture: InputCapture;

  // 注入UI组件模块
  protected floatingBubbleInject: FloatingBubbleInject;
  protected archiveButtonInject: ArchiveButtonInject;

  constructor(config: PlatformConfig) {
    this.config = config;
    // 延迟初始化，等待页面完全渲染
    this.delayedInit();
  }

  /**
   * 延迟初始化，等待页面完全渲染
   */
  private delayedInit() {
    // 使用requestAnimationFrame确保DOM渲染完成
    requestAnimationFrame(() => {
      setTimeout(() => {
        this.initCapture();
        this.initInject();
      }, 500); // 额外延迟500ms确保SPA页面完全加载
    });
  }

  /**
   * 初始化选择器
   */
  private initCapture() {
    this.mergedSelectors = this.mergeSelectors();
    this.inputCapture = new InputCapture(this);
  }

  private initInject() {
    // 初始化UI组件注入器
    this.floatingBubbleInject = new FloatingBubbleInject(this);
    this.archiveButtonInject = new ArchiveButtonInject(this);
  }

  /**
   * 获取平台特定的选择器配置
   * 子类必须实现此方法来提供平台特定的选择器
   */
  abstract getSelectors(): SelectorConfig;

  /**
   * 合并通用选择器和平台特定选择器
   * 子类可以重写此方法来自定义合并逻辑
   */
  protected mergeSelectors(): SelectorConfig {
    const platformSelectors = this.getSelectors();
    const common = CommonSelectors;

    // 平台特定选择器优先，然后是通用选择器
    return {
      inputField: [...platformSelectors.inputField, ...common.inputField],
      sendButton: [...platformSelectors.sendButton, ...common.sendButton],
      messageContainer: [
        ...platformSelectors.messageContainer,
        ...common.messageContainer,
      ],
      inputContainer: [
        ...platformSelectors.inputContainer,
        ...common.inputContainer,
      ],
    };
  }

  /**
   * 正则表达式匹配
   */
  protected regexMatch(text: string, pattern: RegExp): RegexMatchResult {
    const match = text.match(pattern);
    return {
      matched: match !== null,
      groups: match || undefined,
      content: match?.[1] || match?.[0] || undefined,
    };
  }

  /**
   * 获取平台配置
   */
  getConfig(): PlatformConfig {
    return this.config;
  }

  /**
   * 设置当前平台信息
   */
  setCurrentPlatform(platform: Platform | null): void {
    this.currentPlatform = platform;
  }
  public getCurrentPlatform(): Platform | null {
    return this.currentPlatform;
  }

  public getInputCapture(): InputCapture {
    return this.inputCapture;
  }

  /**
   * 销毁适配器
   */
  destroy(): void {
    // 清理UI组件
    this.floatingBubbleInject.destroy();
    this.archiveButtonInject.destroy();

    // 清理捕捉模块
    this.inputCapture.destroy();

    console.log(`【EchoSync】${this.config.name} adapter destroyed`);
  }
}
