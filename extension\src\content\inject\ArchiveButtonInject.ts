import { ArchiveButton } from '../components/ArchiveButton'
import { BaseAIAdapter } from '../adapters/BaseAIAdapter'
import { chatHistoryDatabaseProxy, platformDatabaseProxy } from '@/common/service/databaseProxy'
import { DOMUtils } from '../utils/DOMUtils'

/**
 * 存档按钮注入逻辑
 * 负责与页面元素交互、事件监听和业务逻辑处理
 */
export class ArchiveButtonInject {
  public component: ArchiveButton  // 改为public，允许外部访问
  private currentPromptId: string = ''
  private archivedPromptIds: Set<string> = new Set()
  private adapter: BaseAIAdapter = null
  private resizeObserver: ResizeObserver | null = null
  private resizeTimeout: NodeJS.Timeout | null = null

  constructor(adapter) {
    this.adapter = adapter
    this.component = new ArchiveButton()
    this.generateNewPromptId()
    this.setupEventListeners()
    this.inject()
  }

  /**
   * 注入存档按钮到页面
   */
  async inject(): Promise<void> {
    if (!this.adapter) {
      console.warn('【EchoSync】ArchiveButtonInject: No adapter instance available')
      return
    }

    // 渲染UI组件
    const buttonElement = this.component.render()
    document.body.appendChild(buttonElement)

    // 设置点击事件
    this.component.onClick(() => {
      this.archiveCurrentPrompt()
    })

    // 初始定位
    this.repositionButton()
  }


  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听输入框变化事件
    document.addEventListener('input', (e) => {
      const target = e.target as HTMLElement
      if (this.isInputElement(target)) {
        const value = (target as HTMLInputElement).value || target.textContent || ''
        this.updateButtonState(value)
      }
    })
  }

  /**
   * 检查元素是否为输入元素
   */
  private isInputElement(element: HTMLElement): boolean {
    if (!element) return false

    const tagName = element.tagName.toLowerCase()
    const isInput = tagName === 'input' || tagName === 'textarea'
    const isContentEditable = element.contentEditable === 'true'
    const hasRole = element.getAttribute('role') === 'textbox'

    return isInput || isContentEditable || hasRole
  }

  /**
   * 重新定位按钮
   */
  private repositionButton(): void {
    if (!this.adapter) return
    // 直接查找输入元素
    const inputElement = this.adapter.getInputCapture().captureElement()
    this.positionButton(inputElement)
  }

  /**
   * 定位存档按钮
   */
  private positionButton(inputContainer: HTMLElement): void {
    const updatePosition = () => {
      const rect = inputContainer.getBoundingClientRect()
      const buttonSize = 25
      const margin = 8

      let left = rect.right + margin
      let top = rect.bottom - buttonSize

      // 边界检查
      const windowWidth = window.innerWidth
      const windowHeight = window.innerHeight

      left = Math.max(margin, Math.min(left, windowWidth - buttonSize - margin))
      top = Math.max(margin, Math.min(top, windowHeight - buttonSize - margin))

      this.component.updatePosition(left, top)
    }

    updatePosition()

    // 防抖处理
    const debouncedUpdate = () => {
      if (this.resizeTimeout) {
        clearTimeout(this.resizeTimeout)
      }
      this.resizeTimeout = setTimeout(updatePosition, 100)
    }

    window.addEventListener('resize', debouncedUpdate)
    window.addEventListener('scroll', debouncedUpdate)

    // 观察容器变化
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }
    this.resizeObserver = new ResizeObserver(debouncedUpdate)
    this.resizeObserver.observe(inputContainer)

    if (inputContainer.parentElement) {
      this.resizeObserver.observe(inputContainer.parentElement)
    }
  }

  /**
   * 更新按钮状态
   */
  private updateButtonState(inputValue?: string): void {
    let content = inputValue
    const hasContent = (content || '').trim().length > 0
    const isArchived = this.archivedPromptIds.has(this.currentPromptId)

    if (hasContent && !isArchived) {
      this.component.showWithGlow()
    } else if (isArchived) {
      this.component.showArchivedState()
    } else {
      this.component.hide()
    }
  }

  /**
   * 存档当前提示词
   */
  private async archiveCurrentPrompt(): Promise<void> {
    if (!this.adapter) {
      console.error('【EchoSync】ArchiveButtonInject: No adapter instance available')
      return
    }

    const currentPrompt = this.adapter.getInputCapture().getCurrentInput()
    if (!currentPrompt || currentPrompt.trim().length === 0) {
      console.warn('【EchoSync】ArchiveButtonInject: Input is empty, cannot archive')
      return
    }

    try {
      const platformName = this.adapter.getCurrentPlatform()?.name
      if (!platformName) {
        console.error('【EchoSync】ArchiveButtonInject: Cannot identify current platform')
        return
      }

      this.generateNewPromptId()

      // 获取平台信息
      const platformResult = await platformDatabaseProxy.getByName(platformName.toLowerCase())
      if (!platformResult.success || !platformResult.data) {
        console.error('【EchoSync】Platform not found:', platformName)
        return
      }

      const archiveData = {
        chat_prompt: currentPrompt,
        chat_uid: this.currentPromptId,
        platform_id: platformResult.data.id!,
        create_time: Date.now()
      }

      const result = await chatHistoryDatabaseProxy.create(archiveData)

      if (result.success) {
        this.archivedPromptIds.add(this.currentPromptId)
        this.component.showArchivedState()
        this.component.showArchiveAnimation()
        console.log('【EchoSync】Prompt archived successfully:', result.data)
      } else {
        console.error('【EchoSync】Archive failed:', result.error)
      }
    } catch (error) {
      console.error('【EchoSync】Archive prompt error:', error)
    }
  }

  /**
   * 生成新的提示词ID
   */
  private generateNewPromptId(): void {
    const timestamp = Date.now()
    const randomStr = Math.random().toString(36).substring(2, 8)
    this.currentPromptId = `prompt-${timestamp}-${randomStr}`
  }

  /**
   * 获取当前提示词ID
   */
  getCurrentPromptId(): string {
    return this.currentPromptId
  }

  /**
   * 设置当前提示词ID
   */
  setCurrentPromptId(chatUid: string): void {
    this.currentPromptId = chatUid
  }

  /**
   * 标记为已存档状态
   */
  markAsArchived(): void {
    this.archivedPromptIds.add(this.currentPromptId)
    this.component.showArchivedState()
  }

  /**
   * 销毁注入器
   */
  destroy(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
      this.resizeObserver = null
    }

    if (this.resizeTimeout) {
      clearTimeout(this.resizeTimeout)
      this.resizeTimeout = null
    }

    this.component.destroy()

    // 移除事件监听器
    document.removeEventListener('input', this.updateButtonState.bind(this))
    window.removeEventListener('resize', this.repositionButton.bind(this))
    window.removeEventListener('scroll', this.repositionButton.bind(this))

    // 清理数据
    this.archivedPromptIds.clear()
    this.currentPromptId = ''
    this.adapter = null
  }
}
