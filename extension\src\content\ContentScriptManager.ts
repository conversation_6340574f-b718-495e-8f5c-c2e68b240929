import { PlatformDetector } from "@/content/capture/PlatformDetector";
import { ChatGPTAdapter } from "@/content/adapters/chatgpt";
import { DeepSeekAdapter } from "@/content/adapters/deepseek";
import { Claude<PERSON>dapter } from "@/content/adapters/claude";
import { GeminiAdapter } from "@/content/adapters/gemini";
import { KimiAdapter } from "@/content/adapters/kimi";
import { BaseAIAdapter } from "@/content/adapters/BaseAIAdapter";
import { platformDatabaseProxy } from "@/common/service/databaseProxy";
import { checkAndUpdateFavicon } from "@/content/capture/FaviconCapture";
import type {
  ChatHistory,
  Platform,
} from "@/common/types/database_entity";

/**
 * Content Script 管理器
 * 重构后以 BaseAIAdapter 为核心应用上下文，统一管理组件间交互
 */
export class ContentScriptManager {
  private adapter: BaseAIAdapter | null = null;
  private platformDetector: PlatformDetector;
  private isInitialized: boolean = false;

  constructor() {
    this.platformDetector = new PlatformDetector();
    this.init();
  }

  /**
   * 初始化管理器
   * 重构后以 BaseAIAdapter 为核心应用上下文
   */
  async init() {
    if (this.isInitialized) return;

    try {
      // 1. 检测平台
      const platform = this.platformDetector.detectCurrentPlatform();

      if (platform) {
        // 2. 创建适配器
        this.adapter = this.createAdapter(platform.id);

        if (this.adapter) {
          // 4. 加载平台信息并初始化
          await this.loadPlatformInfo();
          
          this.isInitialized = true;
          console.log("【EchoSync】Initialized for:", platform.name);
        }
      }
    } catch (error) {
      console.error("【EchoSync】Init error:", error);
    }
  }

  /**
   * 创建适配器实例作为核心应用上下文
   */
  private createAdapter(platformId: string): BaseAIAdapter | null {
    const adapters = {
      chatgpt: () => new ChatGPTAdapter(),
      deepseek: () => new DeepSeekAdapter(),
      claude: () => new ClaudeAdapter(),
      gemini: () => new GeminiAdapter(),
      kimi: () => new KimiAdapter(),
    };

    return adapters[platformId]?.() || null;
  }

  /**
   * 加载平台信息 比如favicon
   */
  private async loadPlatformInfo(): Promise<void> {
    if (!this.adapter) return;

    try {
      const platformsResult = await platformDatabaseProxy.getAll();

      if (platformsResult.success) {
        const config = this.adapter.getConfig();
        // 找到当前平台
        const currentPlatform: Platform =
          platformsResult.data.find((p: any) => {
            const nameMatch = p.name === config.name;
            const urlMatch = window.location.href.includes(
              p.url.replace("https://", "").replace("http://", "")
            );
            return nameMatch || urlMatch;
          }) || null;

        if (currentPlatform?.id) {
          // 更新平台favicon
          checkAndUpdateFavicon(currentPlatform).catch(console.error);
        }

        this.adapter.setCurrentPlatform(currentPlatform);
        console.log(
          "【EchoSync】Platform info loaded via MessagingService:",
          currentPlatform
        );
      } else {
        console.warn(
          "【EchoSync】Failed to load platform info:",
          platformsResult.error
        );
      }
    } catch (error) {
      console.error("【EchoSync】Error loading platform info:", error);
    }
  }

  /**
   * 销毁管理器
   * 清理核心应用上下文和所有依赖组件
   */
  destroy(): void {
    if (this.adapter) {
      this.adapter.destroy();
      this.adapter = null;
      console.log(
        "【EchoSync】Core application context (BaseAIAdapter) destroyed"
      );
    }

    // 清理其他组件
    this.isInitialized = false;
    console.log("【EchoSync】ContentScriptManager destroyed completely");
  }
}
